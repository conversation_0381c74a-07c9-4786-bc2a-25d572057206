# ShadCN Components Used in FitForJob

This document tracks all ShadCN UI components used in the FitForJob project to avoid duplicate installations and maintain consistency.

## Installed Components

### Core UI Components
- **button** - Primary action buttons throughout the application
- **card** - Content containers for forms, responses, and sections
- **form** - Form wrapper components with validation
- **input** - Text input fields for job details
- **label** - Form field labels
- **textarea** - Multi-line text inputs for descriptions

### Layout & Navigation
- **tabs** - Content organization in response pages
- **separator** - Visual dividers between sections
- **sheet** - Side panels and overlays (if used)

### Feedback & Status
- **progress** - Progress bars for upload and generation status
- **skeleton** - Loading placeholders
- **alert-dialog** - Confirmation dialogs
- **tooltip** - Contextual help and information

### Notifications
- **sonner** - Toast notifications system

### Theme
- **theme-provider** - Dark/light theme management

## Component Usage Reference

### Button Component
Used in:
- `components/JobForm.tsx` - Generate AI Responses button
- `sections/HeroSection.tsx` - Get Started Now CTA
- `app/response/[id]/page.tsx` - Navigation and action buttons
- `components/Navbar.tsx` - Navigation buttons

### Card Component
Used in:
- `components/JobForm.tsx` - Main form container
- `app/response/[id]/page.tsx` - Response content containers
- `components/ResponseSection.tsx` - Individual response sections

### Form Components (form, input, label, textarea)
Used in:
- `components/JobForm.tsx` - Main job application form
- Form validation with React Hook Form integration

### Tabs Component
Used in:
- `app/response/[id]/page.tsx` - Application Materials and Interview Prep tabs

### Progress Component
Used in:
- `components/JobForm.tsx` - Status indicator for parsing and generation

### Skeleton Component
Used in:
- `app/response/[id]/page.tsx` - Loading states for response content

## Installation Commands Used

```bash
# Core components
npx shadcn@latest add button
npx shadcn@latest add card
npx shadcn@latest add form
npx shadcn@latest add input
npx shadcn@latest add label
npx shadcn@latest add textarea

# Layout components
npx shadcn@latest add tabs
npx shadcn@latest add separator
npx shadcn@latest add sheet

# Feedback components
npx shadcn@latest add progress
npx shadcn@latest add skeleton
npx shadcn@latest add alert-dialog
npx shadcn@latest add tooltip

# Notifications
npx shadcn@latest add sonner
```

## Configuration

ShadCN is configured with:
- **Style**: New York
- **Base Color**: Neutral
- **CSS Variables**: Enabled
- **Icon Library**: Lucide React
- **RSC**: Enabled
- **TypeScript**: Enabled

Configuration file: `components.json`

## Notes

- All components follow the New York style variant
- Components are customized with Tailwind CSS classes
- Dark theme is the default with proper color schemes
- Components integrate with React Hook Form for validation
- Motion library is used for animations on top of ShadCN components
