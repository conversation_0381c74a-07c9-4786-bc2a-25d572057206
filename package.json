{"name": "job-interview-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.7", "@clerk/nextjs": "^6.15.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "ai": "^4.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.487.0", "mongoose": "^8.13.2", "motion": "^12.6.5", "next": "15.2.4", "next-themes": "^0.4.6", "pdf-parse": "^1.1.1", "posthog-js": "^1.236.0", "posthog-node": "^4.11.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-loading-indicators": "^1.0.1", "sonner": "^2.0.3", "svix": "^1.64.1", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/body-parser": "^1.19.5", "@types/express": "^5.0.2", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/pdf-parse": "^1.1.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "prettier": "3.5.3", "tailwindcss": "^4", "typescript": "^5"}}